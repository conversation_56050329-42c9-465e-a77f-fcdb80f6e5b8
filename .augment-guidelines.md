# Nixtla Forecasting Ensemble - Augment Guidelines

*Project-specific guidelines for elite development partnership in forecasting research and implementation*

## 🎯 Project Context

<ProjectIdentity>
  <Focus>Nixtla Ecosystem Mastery</Focus>
  <Libraries>StatsForecast, MLForecast, NeuralForecast</Libraries>
  <Purpose>Research-driven forecasting system development with comprehensive knowledge management</Purpose>
  <Approach>Data-source agnostic, hierarchical planning, core-first implementation</Approach>
</ProjectIdentity>

## 📚 Research Document Standards

<ResearchWorkflow priority="CRITICAL">
  <SystematicAnalysis>
    For ALL new research documents:
    1. **Use sequential-thinking-tools** for comprehensive analysis
    2. **Assess document scope**: Content overlap, integration strategy, organization impact
    3. **Evaluate metadata requirements**: Ensure all required fields present
    4. **Determine placement**: Follow established directory structure
    5. **Plan cross-references**: Identify related documents and concepts
    6. **Update knowledge graph**: Maintain basic-memory connectivity
  </SystematicAnalysis>

  <MetadataStandards enforcement="MANDATORY">
    Required YAML frontmatter for all research documents:
    ```yaml
    ---
    title: "Descriptive Title"
    permalink: "category/subcategory/document-name"
    type: "implementation-guide|technical-research|note|synthesis|etc"
    created: "YYYY-MM-DD"
    last_updated: "YYYY-MM-DD"
    tags: ["forecasting", "bitcoin", "nixtla", "specific-techniques"]
    models: ["AutoARIMA", "ETS", "LSTM", "etc"]
    techniques: ["ensemble", "decomposition", "feature-engineering"]
    libraries: ["statsforecast", "mlforecast", "neuralforecast"]
    complexity: 1-10
    datasets: ["bitcoin-price", "financial-time-series"]
    summary: "Brief description of document content and purpose"
    related: ["permalink1", "permalink2"]
    ---
    ```
  </MetadataStandards>

  <OrganizationPrinciples>
    **Directory Structure Adherence:**
    - `/Research/01-Foundations/`: Core concepts, models, evaluation methods
    - `/Research/02-Domain-Applications/`: Cryptocurrency, financial markets
    - `/Research/03-Techniques/`: Decomposition, ensembling, feature engineering
    - `/Research/04-Implementation/`: Code examples, library-specific guides
    - `/Research/05-Case-Studies/`: Benchmarks, performance analysis
    - `/Research/06-Resources/`: Datasets, literature reviews, references

    **Library-Specific Placement:**
    - NeuralForecast documents: `Research/04-Implementation/Libraries/Nixtla/NeuralForecast/`
    - MLForecast documents: `Research/04-Implementation/Libraries/Nixtla/MLForecast/`
    - StatsForecast documents: `Research/04-Implementation/Libraries/Nixtla/StatsForecast/`
  </OrganizationPrinciples>
</ResearchWorkflow>

## 🏗️ Development Patterns

<ImplementationPhilosophy>
  <DataSourceAgnostic>
    Build systems that work with standard formats from any source:
    - Exchange APIs, databases, CSV files, real-time streams
    - Focus on data transformation and normalization layers
    - Avoid tight coupling to specific data providers
  </DataSourceAgnostic>

  <HierarchicalPlanning>
    Always start with high-level overview, then drill down:
    1. **System Architecture**: Overall design and component relationships
    2. **Core Components**: Essential forecasting functionality
    3. **Integration Points**: Data input/output interfaces
    4. **Feature Extensions**: Advanced capabilities and optimizations
    5. **Testing Strategy**: Validation and performance verification
  </HierarchicalPlanning>

  <CoreFirstApproach>
    Prioritize core forecasting system before adding complexity:
    - Basic model implementation and evaluation
    - Data preprocessing and feature engineering
    - Ensemble methodology and combination strategies
    - Performance metrics and validation frameworks
    - THEN: Advanced features, UI, real-time processing
  </CoreFirstApproach>
</ImplementationPhilosophy>

## 🔧 Tool Integration Patterns

<NixtlaSpecificWorkflow>
  <LibraryIntegration>
    When working with Nixtla libraries:
    1. **StatsForecast**: Classical statistical models (ARIMA, ETS, etc.)
    2. **MLForecast**: Machine learning models with feature engineering
    3. **NeuralForecast**: Deep learning models (LSTM, Transformer, etc.)
    4. **Cross-library ensembles**: Combine predictions from multiple libraries
  </LibraryIntegration>

  <ImplementationSequence>
    Standard development flow:
    1. **Research Analysis**: Use sequential-thinking-tools for document assessment
    2. **Planning**: Create Software Planning MCP tasks with complexity scores
    3. **Implementation**: Work with provided data, avoid API complexity initially
    4. **Documentation**: Store decisions in Basic Memory MCP with cross-references
    5. **Validation**: Test with multiple datasets and scenarios
  </ImplementationSequence>
</NixtlaSpecificWorkflow>

## 📊 Knowledge Management Integration

<MemoryOptimization>
  <CrossReferencing priority="HIGH">
    Maintain comprehensive knowledge graph connectivity:
    - Link related research documents via permalinks
    - Reference implementation guides from theoretical documents
    - Connect case studies to relevant techniques and models
    - Update index files when adding new content
  </CrossReferencing>

  <MetadataConsistency>
    Watch for and correct common issues:
    - Duplicate YAML frontmatter blocks
    - Broken cross-reference permalinks
    - Missing required metadata fields
    - Inconsistent permalink formatting
    - Outdated last_updated timestamps
  </MetadataConsistency>

  <KnowledgeGraphOptimization>
    Before implementation phases:
    1. **Standardize metadata** across all research documents
    2. **Optimize cross-references** for better connectivity
    3. **Validate permalink consistency** throughout the system
    4. **Update index files** to reflect current organization
    5. **Sync basic-memory** to ensure graph accuracy
  </KnowledgeGraphOptimization>
</MemoryOptimization>

## 🎯 Session Workflow Patterns

<ProjectSpecificInitialization>
  <ResearchSessions>
    For research document analysis:
    1. Load recent research activity context
    2. Check for pending integration tasks
    3. Review metadata standardization status
    4. Identify cross-referencing opportunities
  </ResearchSessions>

  <ImplementationSessions>
    For development work:
    1. Load relevant research context from basic-memory
    2. Check Software Planning MCP for active tasks
    3. Review core forecasting system status
    4. Ensure data-source agnostic approach
  </ImplementationSessions>
</ProjectSpecificInitialization>

## 🔍 Quality Assurance

<ValidationChecklist>
  <ResearchDocuments>
    Before finalizing any research document:
    ✓ Complete YAML frontmatter with all required fields
    ✓ Proper directory placement following organization standards
    ✓ Cross-references to related documents via permalinks
    ✓ Summary accurately reflects content and purpose
    ✓ Tags include relevant models, techniques, and libraries
    ✓ Complexity score reflects implementation difficulty
  </ResearchDocuments>

  <Implementation>
    Before completing development tasks:
    ✓ Data-source agnostic design verified
    ✓ Core functionality prioritized over features
    ✓ Package managers used for dependency management
    ✓ Integration with Nixtla ecosystem validated
    ✓ Documentation updated in basic-memory
    ✓ Planning MCP tasks marked complete
  </Implementation>
</ValidationChecklist>

## 🚀 Project-Specific Commands

<QuickActions>
  Research Analysis: "Analyze this research document systematically"
  → Triggers sequential-thinking-tools analysis with metadata assessment

  Integration Planning: "Plan integration for [document/feature]"
  → Creates hierarchical implementation plan in Software Planning MCP

  Knowledge Sync: "Sync research knowledge graph"
  → Updates basic-memory, validates cross-references, checks metadata

  Core Focus: "Prioritize core forecasting functionality"
  → Reviews current tasks, ensures core-first approach maintained
</QuickActions>

---

*These guidelines enhance the existing system configuration for optimal Nixtla forecasting research and development workflows.*
